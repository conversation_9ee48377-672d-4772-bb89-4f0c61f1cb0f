<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToGtSystemTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add dm_prod column to gt_systems if it doesn't exist
        if (!Schema::hasColumn('gt_systems', 'dm_prod')) {
            Schema::table('gt_systems', function (Blueprint $table) {
                $table->tinyInteger('dm_prod')->nullable()->after('prod');
            });
        }

        // Add dm_prod column to gt_system_software_versions if it doesn't exist
        if (!Schema::hasColumn('gt_system_software_versions', 'dm_prod')) {
            Schema::table('gt_system_software_versions', function (Blueprint $table) {
                $table->tinyInteger('dm_prod')->nullable()->after('prod');
            });
        }

        // Add dm_prod column to gt_system_software_files if it doesn't exist
        if (!Schema::hasColumn('gt_system_software_files', 'dm_prod')) {
            Schema::table('gt_system_software_files', function (Blueprint $table) {
                $table->tinyInteger('dm_prod')->nullable()->after('prod');
            });
        }

        // Add dm_prod column to gt_operating_system_files if it doesn't exist
        if (!Schema::hasColumn('gt_operating_system_files', 'dm_prod')) {
            Schema::table('gt_operating_system_files', function (Blueprint $table) {
                $table->tinyInteger('dm_prod')->nullable()->after('prod');
            });
        }

        // Add dm_prod column to gt_systems_update_history if it doesn't exist
        if (!Schema::hasColumn('gt_systems_update_history', 'dm_prod')) {
            Schema::table('gt_systems_update_history', function (Blueprint $table) {
                $table->tinyInteger('dm_prod')->nullable()->after('prod');
            });
        }

        // Create Pivot/lookup table for System software versions and countries if it doesn't exist
        if (!Schema::hasTable('gt_software_versions_countries')) {
            Schema::create('gt_software_versions_countries', function (Blueprint $table) {
                $table->integer('version_id')->unsigned()->index();
                $table->foreign('version_id')->references('id')->on('gt_system_software_versions')->onDelete('cascade');

                $table->integer('country_id')->unsigned()->index();
                $table->foreign('country_id')->references('id')->on('gt_countries')->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('gt_system_tables', function (Blueprint $table) {
            //
        });
    }
}
