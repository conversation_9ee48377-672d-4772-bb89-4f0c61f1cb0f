<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateEifuLibrary extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('eifu_library', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('part_number')->unsigned()->nullable();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->tinyInteger('resource_type')->unsigned()->nullable();
            $table->string('replacement_part_number', '10')->nullable();
            $table->string('replacement_notification_message')->nullable();
            $table->string('replacement_rationale_notes')->nullable();
            $table->timestamp('replacement_date')->nullable();
            $table->string('created_by', '50')->nullable();
            $table->integer('sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default('1')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('eifu_library');
    }
}
