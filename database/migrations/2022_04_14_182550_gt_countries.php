<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class GtCountries extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // iso_3166_countries
        Schema::create('gt_countries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('alpha_2_code');
            $table->string('alpha_3_code');
            $table->integer('numeric');
            $table->boolean('active');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
