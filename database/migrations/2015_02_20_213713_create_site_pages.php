<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSitePages extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('site_pages', function (Blueprint $table) {
            $table->increments('id')->index();
            $table->integer('category_id')->unsigned()->index();
            $table->integer('subcategory_id')->unsigned()->index();
            $table->string('title');
            $table->string('sections');
            $table->string('content');
            $table->string('hero_image');
            $table->string('thumbnail_image');
            $table->string('url');
            $table->string('category_name');
            $table->tinyInteger('product_page')->nullable();
            $table->tinyInteger('new_product')->nullable();
            $table->tinyInteger('featured')->unsigned()->nullable();
            $table->tinyInteger('featured_sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default(0);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->timestamp('published_on');
            $table->timestamps();

            $table->foreign('category_id')->references('id')->on('site_categories')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('site_pages');
    }
}
