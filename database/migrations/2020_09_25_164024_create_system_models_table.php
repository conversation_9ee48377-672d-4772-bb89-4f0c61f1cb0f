<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSystemModelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gt_system_models', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->unique()->index();

            $table->string('system_name');
            $table->string('system_model');

            $table->string('manufacturer_regex');
            $table->string('manufacture_year_regex');
            $table->string('manufacture_month_regex');
            $table->string('system_model_regex');
            $table->string('lot_number_regex');

            $table->tinyInteger('international')->nullable();

            $table->tinyInteger('active')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gt_system_models');
    }
}
