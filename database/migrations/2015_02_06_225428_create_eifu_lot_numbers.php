<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateEifuLotNumbers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('eifu_lot_numbers_jde', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('eifu_lot_numbers_jde_backup_1', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('eifu_lot_numbers_jde_backup_2', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('eifu_lot_numbers_jde_backup_3', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('eifu_lot_numbers_mfgpro', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('eifu_lot_numbers_mfgpro_backup_1', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('eifu_lot_numbers_mfgpro_backup_2', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('eifu_lot_numbers_mfgpro_backup_3', function (Blueprint $table) {
            $table->increments('id');
            $table->string('lot_number', 25)->nullable();
            $table->string('ref', 25)->nullable();
            $table->string('product_code', 25)->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number', 25)->nullable();
            $table->string('resource_type', 25)->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number', false, true)->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('eifu_lot_numbers_jde');
        Schema::drop('eifu_lot_numbers_jde_backup_1');
        Schema::drop('eifu_lot_numbers_jde_backup_2');
        Schema::drop('eifu_lot_numbers_jde_backup_3');
        Schema::drop('eifu_lot_numbers_mfgpro');
        Schema::drop('eifu_lot_numbers_mfgpro_backup_1');
        Schema::drop('eifu_lot_numbers_mfgpro_backup_2');
        Schema::drop('eifu_lot_numbers_mfgpro_backup_3');
    }
}
