<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSystemUpgradeEligibilityTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gt_system_upgrade_eligibility', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->unique()->index();

            $table->string('system_model');
            $table->string('serial_number');

            $table->tinyInteger('internal')->nullable();
            $table->tinyInteger('international')->nullable();

            $table->tinyInteger('active')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gt_system_upgrade_eligibility');
    }
}
