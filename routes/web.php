<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

/** ------------------------------------------
 *  Route constraint patterns
 *  ------------------------------------------.
 */
/*

    Route::pattern('comment', '[0-9]+');
    Route::pattern('post', '[0-9]+');
    Route::pattern('user', '[0-9]+');
    Route::pattern('role', '[0-9]+');
    Route::pattern('token', '[0-9a-z]+');

*/
Route::pattern('user_id', '[0-9]+');
Route::pattern('category', '[0-9a-z-]+');
Route::pattern('product', '[0-9a-zA-Z-]+');
Route::pattern('section', '[0-9a-z-]+');
Route::pattern('shortname', '[0-9a-z-]+');
Route::pattern('program', '[0-9a-z-]+');
Route::pattern('serial_number', '[0-9a-zA-Z-]+');
Route::pattern('lot_number', '[0-9a-zA-Z]+');
Route::pattern('language', '[a-zA-Z]+');

/* ------------------------------------------
*  Frontend & Base Routes
*  ------------------------------------------
*/

// Root route for local development
Route::get('/', function () {
    return view('dev-dashboard');
});

/* ------------------------------------------
    /** Catalog Routes */
Route::group(['prefix' => 'catalog'], function () {
    Route::pattern('category', '[0-9a-z-]+');

    // Return main products page
    Route::get('/', 'CatalogController@index');

    // use Category name / Product Name
    Route::get('{category}/{type?}', 'CatalogController@category');
});

/* ------------------------------------------
    /** elabeling Routes */

// Also use the /elabeling -> just redirector to the /elabeling routes
Route::get('/eifu', function () {
    return Redirect::to('/elit');
});
Route::get('/elabeling', function () {
    return Redirect::to('/elit');
});
Route::get('/eliterature', function () {
    return Redirect::to('/elit');
});

// elabeling Product Search Page
Route::group(['prefix' => 'elit'], function () {
    Route::get('/confirmation', ['as' => 'elabeling.confirmation', 'uses' => 'Elabeling\ElabelingController@confirmation']);
    Route::post('/confirmation', ['as' => 'elabeling.confirmation.post', 'uses' => 'Elabeling\ElabelingController@confirmation']);
    Route::get('/', ['as' => 'elabeling.search', 'uses' => 'Elabeling\ElabelingController@index']);
    // Route::get('/{lot_number}', ['as' => 'elabeling.search.get', 'uses' => 'Elabeling\ElabelingController@getSearch']);
    Route::get('/{lot_number}/{language?}/{type?}', ['as' => 'elabeling.search.get', 'uses' => 'Elabeling\ElabelingController@getSearch']);
    Route::post('/', ['as' => 'elabeling.search.post', 'uses' => 'Elabeling\ElabelingController@search']);
    Route::post('/changelanguage', ['as' => 'elabeling.changelanguage', 'uses' => 'Elabeling\ElabelingController@changeLanguage']);

    Route::get('/part-number/search', ['as' => 'elabeling.search.part.number', 'uses' => 'Admin\Elabeling\ManagementController@partNumber']);
    Route::post('/part-number/search', ['as' => 'elabeling.search.part.number.post', 'uses' => 'Admin\Elabeling\ManagementController@partNumberSearch']);
});

/* ------------------------------------------
    /** Imaging Upgrader Routes */

Route::group(['prefix' => 'products/imaging/upgrade'], function () {
    Route::group(['prefix' => 'uat'], function () {
        Route::pattern('serial_number', '[0-9a-zA-Z-]+');

        //testing routes
        Route::get('/validate/form', ['as' => 'serial.number.validation.form.uat', 'uses' => 'ImagingUpgrade\ImagingUpgradeUatApiController@form']);
        Route::post('/validate', ['as' => 'serial.number.validation.form.uat', 'uses' => 'ImagingUpgrade\ImagingUpgradeUatApiController@serialNumberValidationForm']);

        Route::get('/validate/{serial_number}', ['as' => 'serial.number.validation.uat', 'uses' => 'ImagingUpgrade\ImagingUpgradeUatApiController@serialNumberValidation']);
        Route::post('/detail/create', ['as' => 'serial.number.detail.create.uat', 'uses' => 'ImagingUpgrade\ImagingUpgradeUatApiController@systemUpgradeDetail']);

        Route::get('/download/{serial_number}', ['as' => 'update.file.download.uat', 'uses' => 'ImagingUpgrade\ImagingUpgradeUatApiController@download']);
        Route::get('/download/', ['as' => 'update.file.download.path.uat', 'uses' => 'ImagingUpgrade\ImagingUpgradeUatApiController@download']);

        Route::get('/checksum/{serial_number}', ['as' => 'update.file.checksum.uat', 'uses' => 'ImagingUpgrade\ImagingUpgradeUatApiController@getFileChecksum']);
    });

    Route::pattern('serial_number', '[0-9a-zA-Z-]+');

    //production routes
    Route::get('/install/device/manager/{ca?}', ['as' => 'imaging.software.install.device.manager', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@index']);

    //production routes
    Route::get('/validate/form', ['as' => 'serial.number.validation.form', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@form']);
    Route::post('/validate', ['as' => 'serial.number.validation.form', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@serialNumberValidationForm']);

    Route::get('/validate/{serial_number}', ['as' => 'serial.number.validation', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@serialNumberValidation']);
    Route::post('/detail/create', ['as' => 'serial.number.detail.create', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@systemUpgradeDetail']);

    Route::get('/download/{serial_number}', ['as' => 'update.file.download', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@download']);
    Route::get('/download/', ['as' => 'update.file.download.path', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@download']);

    Route::get('/checksum/{serial_number}', ['as' => 'update.file.checksum', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@getFileChecksum']);

    Route::get('/details', ['as' => 'details', 'uses' => 'ImagingUpgrade\ImagingUpgradeApiController@addSystemNameToSerialNumbersDetails']);
});

// IP Statement both cases and a "." in case user adds a period from the sentence punctuation.
Route::get('ip', ['as' => 'patents', 'uses' => 'SitePagesController@patents']);
Route::get('IP', ['as' => 'patents', 'uses' => 'SitePagesController@patents']);
Route::get('ip.', ['as' => 'patents', 'uses' => 'SitePagesController@patents']);
Route::get('IP.', ['as' => 'patents', 'uses' => 'SitePagesController@patents']);

Route::group(['prefix' => 'products'], function () {
    Route::pattern('category', '[0-9a-z-]+');
    Route::pattern('product', '[0-9a-zA-Z-]+');
    //Route::pattern('section', '[0-9a-z-]+');

    // Return main products page
    Route::get('/', 'SitePagesController@index');

    // use Category name / Product Name
    Route::get('{category}', 'SitePagesController@category');

    Route::get('{category}/sherlock', 'SitePagesController@sherlock');

    // use Category name / Product Name
    Route::get('{category}/{product}', 'SitePagesController@product');
    // use Category name / Product Name / Section Name
    //Route::get('{category}/{product}/{section?}', 'SitePagesController@section');
});

/* ------------------------------------------
        /** Resources Routes */

Route::group(['prefix' => 'resources'], function () {
    return redirect()->to('https://www.bd.com/en-us/products-and-solutions/solutions/vascular-access-management')->setStatusCode(301);
});

Route::get('discoversherlock', function () {
    return redirect()->to('https://www.bd.com/en-us/products-and-solutions/products/product-families/sherlock-3cg-plus-tip-confirmation-system')->setStatusCode(301);
});
