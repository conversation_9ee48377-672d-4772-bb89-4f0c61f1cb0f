<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BardAccess - Local Development</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .status-bar {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
        }
        .content {
            padding: 30px;
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .nav-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #007bff;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .nav-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .nav-card.admin {
            border-left-color: #28a745;
        }
        .nav-card.api {
            border-left-color: #ffc107;
        }
        .nav-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .nav-card p {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 0.9em;
        }
        .nav-link {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.9em;
            transition: background 0.2s;
        }
        .nav-link:hover {
            background: #0056b3;
        }
        .nav-link.admin {
            background: #28a745;
        }
        .nav-link.admin:hover {
            background: #1e7e34;
        }
        .nav-link.api {
            background: #ffc107;
            color: #333;
        }
        .nav-link.api:hover {
            background: #e0a800;
        }
        .tips {
            background: #e7f3ff;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        .tips h3 {
            margin: 0 0 15px 0;
            color: #007bff;
        }
        .tips ul {
            margin: 0;
            padding-left: 20px;
        }
        .tips li {
            margin-bottom: 8px;
            color: #333;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 BardAccess Development Environment</h1>
            <p>Local development server is running successfully!</p>
        </div>

        <div class="status-bar">
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-dot"></div>
                    <span><strong>Environment:</strong> {{ config('app.env') }}</span>
                </div>
                <div class="status-item">
                    <div class="status-dot"></div>
                    <span><strong>Database:</strong> {{ config('database.default') }}</span>
                </div>
                <div class="status-item">
                    <div class="status-dot"></div>
                    <span><strong>Debug:</strong> {{ config('app.debug') ? 'Enabled' : 'Disabled' }}</span>
                </div>
                <div class="status-item">
                    <div class="status-dot"></div>
                    <span><strong>Laravel:</strong> {{ app()->version() }}</span>
                </div>
            </div>
        </div>

        <div class="content">
            <h2>🔗 Quick Navigation</h2>
            <div class="nav-grid">
                <div class="nav-card admin">
                    <h3>🔧 Admin Panel (Maestro)</h3>
                    <p>Access the backend administration interface for managing users, permissions, and system settings.</p>
                    <a href="/dev-login" class="nav-link admin">🔑 Dev Login & Open Admin</a>
                    <a href="/maestro" class="nav-link admin" style="margin-left: 10px;">Direct Admin Access</a>
                </div>

                <div class="nav-card">
                    <h3>📋 E-Literature Search</h3>
                    <p>Search and manage electronic literature and product documentation.</p>
                    <a href="/dev/test-elit" class="nav-link">🧪 Test Version</a>
                    <a href="/elit" class="nav-link" style="margin-left: 10px;">Real Version</a>
                </div>

                <div class="nav-card">
                    <h3>🔍 Device Validation</h3>
                    <p>Validate device serial numbers and check upgrade eligibility.</p>
                    <a href="/dev/test-validation" class="nav-link">🧪 Test Version</a>
                    <a href="/guidance-technologies/validate/form" class="nav-link" style="margin-left: 10px;">Real Version</a>
                </div>

                <div class="nav-card api">
                    <h3>⚡ API Testing</h3>
                    <p>Test API endpoints for guidance technologies and system integrations.</p>
                    <a href="/api/guidance-technologies/time" class="nav-link api">Test API</a>
                </div>

                <div class="nav-card">
                    <h3>📦 Product Catalog</h3>
                    <p>Browse and manage the product catalog and categories.</p>
                    <a href="/dev/test-catalog" class="nav-link">🧪 Test Version</a>
                    <a href="/catalog" class="nav-link" style="margin-left: 10px;">Real Version</a>
                </div>

                <div class="nav-card">
                    <h3>🏥 Products</h3>
                    <p>View product pages and related information.</p>
                    <a href="/products" class="nav-link">View Products</a>
                </div>
            </div>

            <div class="tips">
                <h3>💡 Development Tips</h3>
                <ul>
                    <li><strong>🔑 Authentication:</strong> Use <code>🔑 Dev Login</code> button above to bypass Microsoft SSO for local testing</li>
                    <li><strong>🧪 Test Routes:</strong> Use "Test Version" links to bypass redirect issues during development</li>
                    <li><strong>Admin Access:</strong> After dev login, use <code>/maestro</code> for backend management</li>
                    <li><strong>API Endpoints:</strong> All API routes are prefixed with <code>/api/guidance-technologies</code></li>
                    <li><strong>Database:</strong> SQLite file located at <code>database/database.sqlite</code></li>
                    <li><strong>Logs:</strong> Check <code>storage/logs/laravel.log</code> for debugging information</li>
                    <li><strong>Cache Clear:</strong> Run <code>php artisan cache:clear</code> if you encounter issues</li>
                    <li><strong>Asset Building:</strong> Use <code>npm run build</code> for production or <code>npm run dev</code> for development</li>
                </ul>
            </div>

            <div style="background: #fff3cd; border-radius: 8px; padding: 20px; border-left: 4px solid #ffc107; margin-top: 20px;">
                <h3 style="margin: 0 0 15px 0; color: #856404;">⚠️ Local Development Notes</h3>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Production Authentication:</strong> This app uses Microsoft Azure AD SSO in production</li>
                    <li><strong>Local Bypass:</strong> Created development user: <code><EMAIL></code> / <code>password123</code></li>
                    <li><strong>Redirect Issues:</strong> Some routes redirect to bd.com - use test versions for development</li>
                    <li><strong>Missing Tables:</strong> Some features may need additional database migrations</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
